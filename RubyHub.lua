local Players = game:GetService("Players") or {}
local RunService = game:GetService("RunService") or {}
local UserInputService = game:GetService("UserInputService") or {}
local TweenService = game:GetService("TweenService") or {}
local LocalPlayer = Players.LocalPlayer

-- Safe camera reference
local Camera = workspace.CurrentCamera or {}

-- Color constants (using direct RGB for maximum compatibility)
local BACKGROUND_COLOR = Color3.fromRGB(22, 22, 30)
local SIDEBAR_COLOR = Color3.fromRGB(17, 17, 24)
local ACCENT_COLOR = Color3.fromRGB(237, 66, 69) -- Ruby red
local TEXT_COLOR = Color3.fromRGB(255, 255, 255)
local SECONDARY_TEXT_COLOR = Color3.fromRGB(185, 185, 185)
local BUTTON_COLOR = Color3.fromRGB(32, 32, 45)
local BUTTON_HOVER_COLOR = Color3.fromRGB(42, 42, 58)

-- Script hub main object
local RubyHub = {
    -- Feature states
    Flying = false,
    FlySpeed = 50,
    NoClip = false,
    AimBot = false,
    SilentAim = false,
    ESP = false,
    InfiniteJump = false,
    AntiAFK = false,
    TeamCheck = false,
    SpazzAimbot = false,
    HitboxExpander = false,
    HitboxSize = 5,
    HitboxShape = "Cube",
    JailBypass = false,
    TargetBone = "Head",
    NoRecoil = false,
    TeleportAimbot = false,
    ArsenalAntiCheatBypass = false,

    -- Container references
    GUI = nil,
    MainFrame = nil,
    Sidebar = nil,
    ContentFrame = nil,
    Pages = {},
    CurrentPage = nil,

    -- ESP objects container
    ESPObjects = {},
    AimCircle = nil,

    -- Executor detection flag
    IsExecutorEnvironment = true
}

-- Safe drawing implementation with executor compatibility
RubyHub.Drawing = {
    new = function(drawingType)
        -- Attempt to use native Drawing
        local success, drawingLib = pcall(function() return Drawing end)

        if success and type(drawingLib) == "table" and type(drawingLib.new) == "function" then
            -- Direct capture of Drawing.new output to prevent errors
            local s, obj = pcall(function() return drawingLib.new(drawingType) end)
            if s and obj then return obj end
        end

        -- Fallback implementation if Drawing is unavailable
        return {
            Visible = false,
            Color = Color3.fromRGB(255, 255, 255),
            Thickness = 1,
            Transparency = 1,
            Filled = false,
            Position = Vector2.new(),
            Size = Vector2.new(),
            Text = "",
            Center = false,
            Outline = false,
            OutlineColor = Color3.fromRGB(0, 0, 0),
            Font = 0,
            From = Vector2.new(),
            To = Vector2.new(),
            Radius = 0,
            Remove = function() end
        }
    end
}

-- Basic utility functions with maximum compatibility

-- Safe instance creation function with comprehensive error handling
local function CreateInstance(className, properties)
    -- Force string className
    className = tostring(className)
    properties = properties or {}

    -- Validate parent exists before attempting creation
    if properties.Parent and (type(properties.Parent) ~= "userdata" and type(properties.Parent) ~= "table") then
        properties.Parent = nil
    end

    -- Basic instance creation with error handling
    local instance, errorMessage

    local success = pcall(function()
        instance = Instance.new(className)
    end)

    -- Return a dummy object if creation failed
    if not success or not instance then
        return {
            ClassName = className,
            Name = properties.Name or "FailedInstance",
            Parent = properties.Parent,

            -- Dummy methods to prevent errors
            FindFirstChild = function() return nil end,
            GetChildren = function() return {} end,
            IsA = function() return false end,
            Destroy = function() end,

            -- Safety for function connections
            GetPropertyChangedSignal = function()
                return {Connect = function() return {Disconnect = function() end} end}
            end,
            Connect = function() return {Disconnect = function() end} end,
            MouseEnter = {Connect = function() return {Disconnect = function() end} end},
            MouseLeave = {Connect = function() return {Disconnect = function() end} end},
            MouseButton1Click = {Connect = function() return {Disconnect = function() end} end},
            MouseButton1Down = {Connect = function() return {Disconnect = function() end} end},
            MouseButton1Up = {Connect = function() return {Disconnect = function() end} end},
            InputBegan = {Connect = function() return {Disconnect = function() end} end},
            InputEnded = {Connect = function() return {Disconnect = function() end} end}
        }
    end

    -- Apply properties with error handling
    for property, value in pairs(properties) do
        pcall(function()
            instance[property] = value
        end)
    end

    return instance
end

-- Simple implementation for tweening
local function SimpleTween(instance, properties, duration)
    duration = duration or 0.3

    -- If TweenService is unavailable, use basic property setting
    if not TweenService.Create then
        pcall(function()
            for property, value in pairs(properties) do
                instance[property] = value
            end
        end)
        return {
            Play = function() end,
            Completed = {
                Connect = function() return {Disconnect = function() end} end
            }
        }
    end

    -- Use TweenService if available
    local tweenInfo = TweenInfo.new(duration, Enum.EasingStyle.Quint, Enum.EasingDirection.Out)

    local success, tween = pcall(function()
        return TweenService:Create(instance, tweenInfo, properties)
    end)

    if success and tween then
        return tween
    else
        -- Fallback for failed tween creation
        return {
            Play = function()
                pcall(function()
                    for property, value in pairs(properties) do
                        instance[property] = value
                    end
                end)
            end,
            Completed = {
                Connect = function(_, callback)
                    spawn(function()
                        wait(duration)
                        pcall(callback)
                    end)
                    return {Disconnect = function() end}
                end
            }
        }
    end
end

-- Simple corner with error handling
local function SimpleCorner(instance, radius)
    local corner = CreateInstance("UICorner", {
        CornerRadius = radius or UDim.new(0, 8),
        Parent = instance
    })
    return corner
end

-- Initialization function - creates minimal UI structure
function RubyHub:InitializeUI()
    -- Clean up any existing GUI
    pcall(function()
        if self.GUI and self.GUI.Parent then
            self.GUI:Destroy()
        end
    end)

    -- Find a suitable parent for the GUI
    local guiParent

    -- Try various parent options with maximum compatibility
    local possibleParents = {
        game:GetService("CoreGui"),
        Players.LocalPlayer and Players.LocalPlayer:FindFirstChildOfClass("PlayerGui"),
        game:GetService("StarterGui")
    }

    for _, parent in ipairs(possibleParents) do
        if parent then
            local success = pcall(function()
                -- Test if we can create an object in this parent
                local testObj = Instance.new("ScreenGui")
                testObj.Parent = parent
                testObj:Destroy()
            end)

            if success then
                guiParent = parent
                break
            end
        end
    end

    -- If no suitable parent found, use gethui() as last resort (executor-specific)
    if not guiParent then
        local success, huiFunc = pcall(function() return gethui() end)
        if success and huiFunc then
            guiParent = huiFunc()
        end
    end

    -- Final fallback - try game:GetService("Players").LocalPlayer.PlayerGui directly
    if not guiParent and game:GetService("Players").LocalPlayer then
        guiParent = game:GetService("Players").LocalPlayer:FindFirstChildOfClass("PlayerGui")
    end

    -- Create the ScreenGui
    self.GUI = CreateInstance("ScreenGui", {
        Name = "RubyHub_Gui",
        ResetOnSpawn = false,
        ZIndexBehavior = Enum.ZIndexBehavior.Sibling,
        Parent = guiParent
    })

    -- Simple property overrides for executor compatibility
    pcall(function() self.GUI.IgnoreGuiInset = true end)
    pcall(function() self.GUI.DisplayOrder = 999999 end)

    -- Create main frame
    self.MainFrame = CreateInstance("Frame", {
        Name = "MainFrame",
        BackgroundColor3 = BACKGROUND_COLOR,
        BorderSizePixel = 0,
        Position = UDim2.new(0.5, -250, 0.5, -175), -- Smaller dimensions for compatibility
        Size = UDim2.new(0, 500, 0, 350),
        Parent = self.GUI
    })

    SimpleCorner(self.MainFrame)

    -- Create top bar (for dragging and title)
    self.TopBar = CreateInstance("Frame", {
        Name = "TopBar",
        BackgroundColor3 = SIDEBAR_COLOR,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 30),
        Parent = self.MainFrame
    })

    SimpleCorner(self.TopBar)

    -- Title label with simplified text
    self.TitleLabel = CreateInstance("TextLabel", {
        Name = "TitleLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 10, 0, 0),
        Size = UDim2.new(0, 100, 1, 0),
        Font = Enum.Font.GothamBold,
        Text = "Ruby Hub",
        TextColor3 = ACCENT_COLOR,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = self.TopBar
    })

    -- Username display with proper error handling
    local username = "User"
    pcall(function()
        if Players.LocalPlayer and Players.LocalPlayer.Name then
            username = Players.LocalPlayer.Name
        end
    end)

    self.UsernameDisplay = CreateInstance("TextLabel", {
        Name = "UsernameLabel",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 120, 0, 0),
        Size = UDim2.new(0.5, -120, 1, 0),
        Font = Enum.Font.Gotham,
        Text = username,
        TextColor3 = TEXT_COLOR,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = self.TopBar
    })

    -- Close button (text-based for compatibility)
    self.CloseButton = CreateInstance("TextButton", {
        Name = "CloseButton",
        BackgroundColor3 = SIDEBAR_COLOR,
        BorderSizePixel = 0,
        Position = UDim2.new(1, -25, 0.5, -10),
        Size = UDim2.new(0, 20, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "X",
        TextColor3 = TEXT_COLOR,
        TextSize = 14,
        Parent = self.TopBar
    })

    SimpleCorner(self.CloseButton)

    -- Connect close button
    pcall(function()
        self.CloseButton.MouseButton1Click:Connect(function()
            if self.GUI then
                self.GUI:Destroy()
                self:Cleanup()
            end
        end)
    end)

    -- Minimize button (text-based for compatibility)
    self.MinimizeButton = CreateInstance("TextButton", {
        Name = "MinimizeButton",
        BackgroundColor3 = SIDEBAR_COLOR,
        BorderSizePixel = 0,
        Position = UDim2.new(1, -50, 0.5, -10),
        Size = UDim2.new(0, 20, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = "-",
        TextColor3 = TEXT_COLOR,
        TextSize = 18,
        Parent = self.TopBar
    })

    SimpleCorner(self.MinimizeButton)

    -- Make the main frame draggable
    self:MakeDraggable(self.TopBar)

    -- Create sidebar
    self.Sidebar = CreateInstance("Frame", {
        Name = "Sidebar",
        BackgroundColor3 = SIDEBAR_COLOR,
        BorderSizePixel = 0,
        Size = UDim2.new(0, 50, 1, 0),
        Parent = self.MainFrame
    })

    SimpleCorner(self.Sidebar)

    -- Create content frame
    self.ContentFrame = CreateInstance("Frame", {
        Name = "ContentFrame",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 55, 0, 35),
        Size = UDim2.new(1, -60, 1, -40),
        Parent = self.MainFrame
    })

    -- Create simplified page system (Scripts and Settings)
    self:CreateSimplifiedPages()

    return true
end

-- Draggable functionality with basic implementation
function RubyHub:MakeDraggable(frame)
    -- Basic variables for dragging
    local dragging = false
    local dragInput, mousePos, framePos

    -- Simplified input handling for maximum compatibility
    local function updateDrag(input)
        if not dragging or not self.MainFrame then return end

        local delta = input.Position - mousePos

        -- Direct property update instead of tween for reliability
        pcall(function()
            self.MainFrame.Position = UDim2.new(
                framePos.X.Scale,
                framePos.X.Offset + delta.X,
                framePos.Y.Scale,
                framePos.Y.Offset + delta.Y
            )
        end)
    end

    -- Connect input events with pcall wrapping and debounce
    local dragDebounce = false
    local lastDragTime = 0
    local dragCooldown = 0.05 -- 50ms cooldown for drag updates

    pcall(function()
        frame.InputBegan:Connect(function(input)
            if input.UserInputType == Enum.UserInputType.MouseButton1 then
                -- Only start dragging if not already dragging
                if not dragging then
                    dragging = true
                    mousePos = input.Position
                    framePos = self.MainFrame.Position
                end
            end
        end)
    end)

    pcall(function()
        frame.InputEnded:Connect(function(input)
            if input.UserInputType == Enum.UserInputType.MouseButton1 then
                dragging = false
            end
        end)
    end)

    pcall(function()
        UserInputService.InputChanged:Connect(function(input)
            if input.UserInputType == Enum.UserInputType.MouseMovement then
                -- Add debounce to prevent excessive updates
                local currentTime = tick()
                if currentTime - lastDragTime < dragCooldown then
                    return
                end
                lastDragTime = currentTime

                updateDrag(input)
            end
        end)
    end)
end

-- Create simplified pages for maximum compatibility
function RubyHub:CreateSimplifiedPages()
    -- Initialize pages table
    self.Pages = {}

    -- Create scripts page
    local scriptsButton = CreateInstance("TextButton", {
        Name = "ScriptsButton",
        BackgroundColor3 = ACCENT_COLOR,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 10, 0, 40),
        Size = UDim2.new(0, 30, 0, 30),
        Font = Enum.Font.GothamBold,
        Text = "S",
        TextColor3 = TEXT_COLOR,
        TextSize = 14,
        Parent = self.Sidebar
    })

    SimpleCorner(scriptsButton)

    local scriptsPage = CreateInstance("ScrollingFrame", {
        Name = "ScriptsPage",
        BackgroundTransparency = 1,
        BorderSizePixel = 0,
        ClipsDescendants = true,
        Position = UDim2.new(0, 0, 0, 0),
        Size = UDim2.new(1, 0, 1, 0),
        CanvasSize = UDim2.new(0, 0, 0, 0),
        ScrollBarThickness = 4,
        Visible = true,
        Parent = self.ContentFrame
    })

    -- Create settings page
    local settingsButton = CreateInstance("TextButton", {
        Name = "SettingsButton",
        BackgroundColor3 = BUTTON_COLOR,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 10, 0, 80),
        Size = UDim2.new(0, 30, 0, 30),
        Font = Enum.Font.GothamBold,
        Text = "⚙",
        TextColor3 = SECONDARY_TEXT_COLOR,
        TextSize = 14,
        Parent = self.Sidebar
    })

    SimpleCorner(settingsButton)

    local settingsPage = CreateInstance("ScrollingFrame", {
        Name = "SettingsPage",
        BackgroundTransparency = 1,
        BorderSizePixel = 0,
        ClipsDescendants = true,
        Position = UDim2.new(0, 0, 0, 0),
        Size = UDim2.new(1, 0, 1, 0),
        CanvasSize = UDim2.new(0, 0, 0, 0),
        ScrollBarThickness = 4,
        Visible = false,
        Parent = self.ContentFrame
    })

    -- Store pages in the Pages table
    self.Pages["Scripts"] = {
        Button = scriptsButton,
        Container = scriptsPage
    }

    self.Pages["Settings"] = {
        Button = settingsButton,
        Container = settingsPage
    }

    -- Set current page
    self.CurrentPage = "Scripts"

    -- Connect buttons to show/hide pages
    pcall(function()
        scriptsButton.MouseButton1Click:Connect(function()
            self:ShowPage("Scripts")
        end)
    end)

    pcall(function()
        settingsButton.MouseButton1Click:Connect(function()
            self:ShowPage("Settings")
        end)
    end)

    -- Add auto-sizing layout for each page
    local function setupPageLayout(page)
        local layout = CreateInstance("UIListLayout", {
            Padding = UDim.new(0, 8),
            SortOrder = Enum.SortOrder.LayoutOrder,
            Parent = page
        })

        local padding = CreateInstance("UIPadding", {
            PaddingLeft = UDim.new(0, 8),
            PaddingRight = UDim.new(0, 8),
            PaddingTop = UDim.new(0, 8),
            PaddingBottom = UDim.new(0, 8),
            Parent = page
        })

        -- Update canvas size based on content
        pcall(function()
            layout:GetPropertyChangedSignal("AbsoluteContentSize"):Connect(function()
                page.CanvasSize = UDim2.new(0, 0, 0, layout.AbsoluteContentSize.Y + 16)
            end)
        end)
    end

    setupPageLayout(scriptsPage)
    setupPageLayout(settingsPage)

    -- Initialize page content
    self:InitializePageContent()
end

-- Show page function
function RubyHub:ShowPage(pageName)
    -- Safety check
    if not self.Pages or not self.Pages[pageName] then return end

    -- Hide all pages
    for name, page in pairs(self.Pages) do
        if page.Container then
            page.Container.Visible = false
        end

        if page.Button then
            page.Button.BackgroundColor3 = BUTTON_COLOR
            page.Button.TextColor3 = SECONDARY_TEXT_COLOR
        end
    end

    -- Show selected page
    if self.Pages[pageName].Container then
        self.Pages[pageName].Container.Visible = true
    end

    if self.Pages[pageName].Button then
        page.Button.BackgroundColor3 = ACCENT_COLOR
        page.Button.TextColor3 = TEXT_COLOR
    end

    self.CurrentPage = pageName
end

-- Create category function
function RubyHub:CreateCategory(parent, name)
    -- Create category frame
    local category = CreateInstance("Frame", {
        Name = name .. "Category",
        BackgroundTransparency = 1,
        Size = UDim2.new(1, 0, 0, 30),
        Parent = parent
    })

    -- Create title
    local title = CreateInstance("TextLabel", {
        Name = "Title",
        BackgroundTransparency = 1,
        Size = UDim2.new(1, 0, 0, 25),
        Font = Enum.Font.GothamBold,
        Text = name,
        TextColor3 = ACCENT_COLOR,
        TextSize = 16,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = category
    })

    -- Create underline
    local underline = CreateInstance("Frame", {
        Name = "Underline",
        BackgroundColor3 = ACCENT_COLOR,
        BorderSizePixel = 0,
        Position = UDim2.new(0, 0, 0, 25),
        Size = UDim2.new(0, 30, 0, 2),
        Parent = title
    })

    -- Create content frame
    local content = CreateInstance("Frame", {
        Name = "Content",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 30),
        Size = UDim2.new(1, 0, 0, 0),
        AutomaticSize = Enum.AutomaticSize.Y,
        Parent = category
    })

    -- Add list layout to content
    local layout = CreateInstance("UIListLayout", {
        Padding = UDim.new(0, 8),
        SortOrder = Enum.SortOrder.LayoutOrder,
        Parent = content
    })

    -- Update category size based on content size
    pcall(function()
        layout:GetPropertyChangedSignal("AbsoluteContentSize"):Connect(function()
            category.Size = UDim2.new(1, 0, 0, 30 + layout.AbsoluteContentSize.Y)
        end)
    end)

    return content
end

-- Create button function
function RubyHub:CreateButton(parent, text, callback)
    local button = CreateInstance("TextButton", {
        Name = text .. "Button",
        BackgroundColor3 = BUTTON_COLOR,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 36),
        Font = Enum.Font.GothamSemibold,
        Text = text,
        TextColor3 = TEXT_COLOR,
        TextSize = 14,
        Parent = parent
    })

    SimpleCorner(button)

    -- Connect button click event
    pcall(function()
        button.MouseButton1Click:Connect(function()
            pcall(callback or function() end)
        end)
    end)

    -- Hover effects
    pcall(function()
        button.MouseEnter:Connect(function()
            button.BackgroundColor3 = BUTTON_HOVER_COLOR
        end)

        button.MouseLeave:Connect(function()
            button.BackgroundColor3 = BUTTON_COLOR
        end)
    end)

    return button
end

-- Create toggle function
function RubyHub:CreateToggle(parent, text, callback)
    local container = CreateInstance("Frame", {
        Name = text .. "Toggle",
        BackgroundColor3 = BUTTON_COLOR,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 36),
        Parent = parent
    })

    SimpleCorner(container)

    local label = CreateInstance("TextLabel", {
        Name = "Label",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 10, 0, 0),
        Size = UDim2.new(1, -60, 1, 0),
        Font = Enum.Font.GothamSemibold,
        Text = text,
        TextColor3 = TEXT_COLOR,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = container
    })

    local toggleBackground = CreateInstance("Frame", {
        Name = "ToggleBackground",
        BackgroundColor3 = Color3.fromRGB(70, 70, 80),
        BorderSizePixel = 0,
        Position = UDim2.new(1, -45, 0.5, -8),
        Size = UDim2.new(0, 35, 0, 16),
        Parent = container
    })

    SimpleCorner(toggleBackground, UDim.new(1, 0))

    local toggleIndicator = CreateInstance("Frame", {
        Name = "Indicator",
        BackgroundColor3 = Color3.fromRGB(255, 255, 255),
        BorderSizePixel = 0,
        Position = UDim2.new(0, 2, 0.5, -6),
        Size = UDim2.new(0, 12, 0, 12),
        Parent = toggleBackground
    })

    SimpleCorner(toggleIndicator, UDim.new(1, 0))

    -- Toggle state
    local enabled = false

    -- Toggle function
    local function updateToggle()
        enabled = not enabled

        local newPosition = enabled
            and UDim2.new(1, -14, 0.5, -6)
            or UDim2.new(0, 2, 0.5, -6)

        local newColor = enabled
            and ACCENT_COLOR
            or Color3.fromRGB(70, 70, 80)

        -- Direct property updates
        pcall(function()
            toggleIndicator.Position = newPosition
            toggleBackground.BackgroundColor3 = newColor
        end)

        -- Call callback
        pcall(function()
            if callback then
                callback(enabled)
            end
        end)
    end

    -- Connect click event
    pcall(function()
        container.InputBegan:Connect(function(input)
            if input.UserInputType == Enum.UserInputType.MouseButton1 then
                updateToggle()
            end
        end)
    end)

    -- Hover effects
    pcall(function()
        container.MouseEnter:Connect(function()
            container.BackgroundColor3 = BUTTON_HOVER_COLOR
        end)

        container.MouseLeave:Connect(function()
            container.BackgroundColor3 = BUTTON_COLOR
        end)
    end)

    return {
        Container = container,
        SetState = function(state)
            if state ~= enabled then
                updateToggle()
            end
        end
    }
end

-- Create slider function
function RubyHub:CreateSlider(parent, text, min, max, default, callback)
    min = min or 0
    max = max or 100
    default = default or min

    local container = CreateInstance("Frame", {
        Name = text .. "Slider",
        BackgroundColor3 = BUTTON_COLOR,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 50),
        Parent = parent
    })

    SimpleCorner(container)

    local label = CreateInstance("TextLabel", {
        Name = "Label",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 10, 0, 5),
        Size = UDim2.new(1, -70, 0, 20),
        Font = Enum.Font.GothamSemibold,
        Text = text,
        TextColor3 = TEXT_COLOR,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = container
    })

    local valueLabel = CreateInstance("TextLabel", {
        Name = "Value",
        BackgroundColor3 = SIDEBAR_COLOR,
        BorderSizePixel = 0,
        Position = UDim2.new(1, -55, 0, 5),
        Size = UDim2.new(0, 45, 0, 20),
        Font = Enum.Font.GothamBold,
        Text = tostring(default),
        TextColor3 = ACCENT_COLOR,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Center,
        Parent = container
    })

    SimpleCorner(valueLabel)

    local sliderTrack = CreateInstance("Frame", {
        Name = "Track",
        BackgroundColor3 = Color3.fromRGB(40, 40, 50),
        BorderSizePixel = 0,
        Position = UDim2.new(0, 10, 0, 32),
        Size = UDim2.new(1, -20, 0, 6),
        Parent = container
    })

    SimpleCorner(sliderTrack, UDim.new(1, 0))

    local sliderFill = CreateInstance("Frame", {
        Name = "Fill",
        BackgroundColor3 = ACCENT_COLOR,
        BorderSizePixel = 0,
        Size = UDim2.new((default - min) / (max - min), 0, 1, 0),
        Parent = sliderTrack
    })

    SimpleCorner(sliderFill, UDim.new(1, 0))

    local sliderKnob = CreateInstance("Frame", {
        Name = "Knob",
        BackgroundColor3 = Color3.fromRGB(255, 255, 255),
        BorderSizePixel = 0,
        Position = UDim2.new((default - min) / (max - min), -6, 0.5, -6),
        Size = UDim2.new(0, 12, 0, 12),
        ZIndex = 2,
        Parent = sliderTrack
    })

    SimpleCorner(sliderKnob, UDim.new(1, 0))

    -- Slider functionality
    local dragging = false

    local function updateSlider(input)
        local relativePos = math.clamp((input.Position.X - sliderTrack.AbsolutePosition.X) / sliderTrack.AbsoluteSize.X, 0, 1)
        local value = math.floor(min + ((max - min) * relativePos))

        pcall(function()
            sliderFill.Size = UDim2.new(relativePos, 0, 1, 0)
            sliderKnob.Position = UDim2.new(relativePos, -6, 0.5, -6)
            valueLabel.Text = tostring(value)

            if callback then
                callback(value)
            end
        end)
    end

    -- Connect input events
    pcall(function()
        sliderTrack.InputBegan:Connect(function(input)
            if input.UserInputType == Enum.UserInputType.MouseButton1 then
                dragging = true
                updateSlider(input)
            end
        end)

        sliderTrack.InputEnded:Connect(function(input)
            if input.UserInputType == Enum.UserInputType.MouseButton1 then
                dragging = false
            end
        end)

        UserInputService.InputChanged:Connect(function(input)
            if dragging and input.UserInputType == Enum.UserInputType.MouseMovement then
                updateSlider(input)
            end
        end)
    end)

    -- Hover effects
    pcall(function()
        container.MouseEnter:Connect(function()
            container.BackgroundColor3 = BUTTON_HOVER_COLOR
        end)

        container.MouseLeave:Connect(function()
            container.BackgroundColor3 = BUTTON_COLOR
        end)
    end)

    return {
        Container = container,
        SetValue = function(value)
            local normalizedValue = (value - min) / (max - min)

            pcall(function()
                sliderFill.Size = UDim2.new(normalizedValue, 0, 1, 0)
                sliderKnob.Position = UDim2.new(normalizedValue, -6, 0.5, -6)
                valueLabel.Text = tostring(value)

                if callback then
                    callback(value)
                end
            end)
        end
    }
end

-- Initialize page content with all features
function RubyHub:InitializePageContent()
    -- Get pages
    local scriptsPage = self.Pages["Scripts"] and self.Pages["Scripts"].Container
    local settingsPage = self.Pages["Settings"] and self.Pages["Settings"].Container

    if not scriptsPage or not settingsPage then return end

    -- Scripts page content
    local movementCategory = self:CreateCategory(scriptsPage, "Movement")

    -- Fly toggle
    local flyToggle = self:CreateToggle(movementCategory, "Fly", function(enabled)
        self.Flying = enabled
        self:ImplementFly()
    end)

    -- Fly speed slider
    local flySpeedSlider = self:CreateSlider(movementCategory, "Fly Speed", 1, 200, 50, function(value)
        self.FlySpeed = value
    end)

    -- NoClip toggle
    local noclipToggle = self:CreateToggle(movementCategory, "NoClip", function(enabled)
        self.NoClip = enabled
        self:ImplementNoClip()
    end)

    -- Speed category
    local speedCategory = self:CreateCategory(movementCategory, "Speed")

    -- Speed slider
    local speedSlider = self:CreateSlider(speedCategory, "Character Speed", 1, 5000, 16, function(value)
        local character = LocalPlayer.Character
        if character and character:FindFirstChildOfClass("Humanoid") then
            local humanoid = character:FindFirstChildOfClass("Humanoid")
            pcall(function()
                humanoid.WalkSpeed = value
            end)
        end
    end)

    -- Combat category
    local combatCategory = self:CreateCategory(scriptsPage, "Combat")

    -- Team Check toggle
    local teamCheckToggle = self:CreateToggle(combatCategory, "Team Check", function(enabled)
        self.TeamCheck = enabled

        -- Update relevant features
        if self.SilentAim then self:ImplementSilentAim() end
        if self.AimBot then self:ImplementAimbot() end
        if self.ESP then self:ImplementESP() end
    end)

    -- Aimbot toggle
    local aimbotToggle = self:CreateToggle(combatCategory, "Aimbot", function(enabled)
        self.AimBot = enabled
        self:ImplementAimbot()
    end)

    -- Spazz Aimbot toggle
    local spazzAimbotToggle = self:CreateToggle(combatCategory, "Spazz Aimbot", function(enabled)
        self.SpazzAimbot = enabled
        self:ImplementSpazzAimbot()
    end)

    -- Silent Aim toggle
    local silentAimToggle = self:CreateToggle(combatCategory, "Silent Aim", function(enabled)
        self.SilentAim = enabled
        self:ImplementSilentAim()
    end)

    -- ESP toggle
    local espToggle = self:CreateToggle(combatCategory, "ESP", function(enabled)
        self.ESP = enabled
        self:ImplementESP()
    end)

    -- Hitbox Expander toggle
    local hitboxExpanderToggle = self:CreateToggle(combatCategory, "Hitbox Expander", function(enabled)
        self.HitboxExpander = enabled
        self:ImplementHitboxExpander()
    end)

    -- No Recoil toggle
    local noRecoilToggle = self:CreateToggle(combatCategory, "No Recoil", function(enabled)
        self.NoRecoil = enabled
        self:ImplementNoRecoil()
    end)

    -- Hitbox Adjustment category
    local hitboxAdjustmentCategory = self:CreateCategory(combatCategory, "Hitbox Adjustment")

    -- Hitbox size slider
    local hitboxSizeSlider = self:CreateSlider(hitboxAdjustmentCategory, "Hitbox Size", 1, 50, 5, function(value)
        self.HitboxSize = value
        self:UpdateHitboxSettings()
    end)

    -- Hitbox shape dropdown (simplified as buttons for compatibility)
    local hitboxShapeLabel = CreateInstance("TextLabel", {
        Name = "HitboxShapeLabel",
        BackgroundTransparency = 1,
        Size = UDim2.new(1, 0, 0, 20),
        Font = Enum.Font.GothamSemibold,
        Text = "Hitbox Shape:",
        TextColor3 = TEXT_COLOR,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = hitboxAdjustmentCategory
    })

    local cubeButton = self:CreateButton(hitboxAdjustmentCategory, "Cube", function()
        self.HitboxShape = "Cube"
        self:UpdateHitboxSettings()
    end)

    local sphereButton = self:CreateButton(hitboxAdjustmentCategory, "Sphere", function()
        self.HitboxShape = "Sphere"
        self:UpdateHitboxSettings()
    end)

    -- Bone Prioritization category
    local boneCategory = self:CreateCategory(combatCategory, "Bone Prioritization")

    -- Dropdown for bone selection
    local boneLabel = CreateInstance("TextLabel", {
        Name = "BoneLabel",
        BackgroundTransparency = 1,
        Size = UDim2.new(1, 0, 0, 20),
        Font = Enum.Font.GothamSemibold,
        Text = "Target Bone:",
        TextColor3 = TEXT_COLOR,
        TextSize = 14,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = boneCategory
    })

    local headButton = self:CreateButton(boneCategory, "Head", function()
        if not self.AimBot then
            self:DisplayNotification("Enable Aimbot to select a target bone", 3)
            return
        end
        self.TargetBone = "Head"
        self:DisplayNotification("Targeting Head")
        self:ImplementAimbot()
    end)

    local torsoButton = self:CreateButton(boneCategory, "Torso", function()
        if not self.AimBot then
            self:DisplayNotification("Enable Aimbot to select a target bone", 3)
            return
        end

        -- Check if character uses R15 or R6 rig
        local character = LocalPlayer.Character
        if character then
            if character:FindFirstChild("UpperTorso") then
                -- R15 rig
                self.TargetBone = "UpperTorso"
            else
                -- R6 rig
                self.TargetBone = "Torso"
            end
        else
            -- Default to Torso if no character
            self.TargetBone = "Torso"
        end

        self:DisplayNotification("Targeting Torso")
        self:ImplementAimbot()
    end)

    local customButton = self:CreateButton(boneCategory, "Custom", function()
        if not self.AimBot then
            self:DisplayNotification("Enable Aimbot to select a target bone", 3)
            return
        end
        self.TargetBone = "Custom"
        self:DisplayNotification("Targeting Custom Part")
        self:ImplementAimbot()
    end)

    -- Default target bone
    self.TargetBone = "Head"

    -- Miscellaneous category
    local miscCategory = self:CreateCategory(scriptsPage, "Miscellaneous")

    -- Infinite Jump toggle
    local infiniteJumpToggle = self:CreateToggle(miscCategory, "Infinite Jump", function(enabled)
        self.InfiniteJump = enabled
        self:ImplementInfiniteJump()
    end)

    -- Anti AFK toggle
    local antiAFKToggle = self:CreateToggle(miscCategory, "Anti AFK", function(enabled)
        self.AntiAFK = enabled
        self:ImplementAntiAFK()
    end)

    -- Teleport category
    local teleportCategory = self:CreateCategory(scriptsPage, "Teleport")

    -- Input box for username
    local teleportInput = CreateInstance("TextBox", {
        Name = "TeleportInput",
        BackgroundColor3 = BUTTON_COLOR,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 36),
        Font = Enum.Font.GothamSemibold,
        PlaceholderText = "Enter Roblox Username",
        Text = "",
        TextColor3 = TEXT_COLOR,
        TextSize = 14,
        Parent = teleportCategory
    })

    SimpleCorner(teleportInput)

    -- Connect Enter key to teleport functionality
    teleportInput.FocusLost:Connect(function(enterPressed)
        if enterPressed then
            local username = teleportInput.Text
            if username and username ~= "" then
                self:TeleportToPlayer(username)
            end
        end
    end)

    -- Add Teleport Aimbot feature to the scripts page
    local teleportAimbotCategory = self:CreateCategory(scriptsPage, "Teleport Aimbot")

    -- Teleport Aimbot toggle
    local teleportAimbotToggle = self:CreateToggle(teleportAimbotCategory, "Teleport Aimbot", function(enabled)
        self.TeleportAimbot = enabled
        self:ImplementTeleportAimbot()
    end)

    -- Settings page content
    local uiCategory = self:CreateCategory(settingsPage, "UI Settings")

    -- UI theme preset
    local themeButton = self:CreateButton(uiCategory, "Theme: Ruby Red", function()
        -- Basic theme change - simulated
        self:DisplayNotification("Theme switched")
    end)

    -- Hide Keybinds toggle
    local hideKeybindsToggle = self:CreateToggle(uiCategory, "Hide Keybinds", function(enabled)
        -- Simulated functionality
    end)

    -- Add keybind info to the existing UI category
    local keybindInfo = CreateInstance("TextLabel", {
        Name = "KeybindInfo",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 0, 0, 0),
        Size = UDim2.new(1, 0, 0, 20),
        Font = Enum.Font.Gotham,
        Text = "Keybinds: Q - Fly, O - Aimbot",
        TextColor3 = SECONDARY_TEXT_COLOR,
        TextSize = 12,
        TextXAlignment = Enum.TextXAlignment.Left,
        Parent = uiCategory
    })

    -- Admin Protection category
    local adminProtectionCategory = self:CreateCategory(settingsPage, "Admin Protection")

    -- Jail Bypass toggle
    local jailBypassToggle = self:CreateToggle(adminProtectionCategory, "Jail Bypass", function(enabled)
        self.JailBypass = enabled
        self:ImplementJailBypass()
    end)

    -- Game-Specific Features category
    local gameSpecificCategory = self:CreateCategory(settingsPage, "Game-Specific Features")

    -- Arsenal Anti-Cheat Bypass toggle
    local arsenalAntiCheatBypassToggle = self:CreateToggle(gameSpecificCategory, "Arsenal Anti-Cheat Bypass", function(enabled)
        self.ArsenalAntiCheatBypass = enabled
        self:ImplementArsenalAntiCheatBypass()
    end)

    -- About category
    local aboutCategory = self:CreateCategory(settingsPage, "About")

    -- Create about information
    local infoFrame = CreateInstance("Frame", {
        Name = "InfoFrame",
        BackgroundColor3 = BUTTON_COLOR,
        BorderSizePixel = 0,
        Size = UDim2.new(1, 0, 0, 70),
        Parent = aboutCategory
    })

    SimpleCorner(infoFrame)

    local infoText = CreateInstance("TextLabel", {
        Name = "InfoText",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 10, 0, 10),
        Size = UDim2.new(1, -20, 1, -20),
        Font = Enum.Font.Gotham,
        Text = "Ruby Hub v1.0\nExecutor-Optimized\n\nPowered by Luau",
        TextColor3 = TEXT_COLOR,
        TextSize = 14,
        TextWrapped = true,
        TextXAlignment = Enum.TextXAlignment.Left,
        TextYAlignment = Enum.TextYAlignment.Top,
        Parent = infoFrame
    })
end

-- Teleport to player function
function RubyHub:TeleportToPlayer(username)
    pcall(function()
        local targetPlayer = Players:FindFirstChild(username)
        if targetPlayer and targetPlayer.Character and targetPlayer.Character:FindFirstChild("HumanoidRootPart") then
            local targetHRP = targetPlayer.Character.HumanoidRootPart
            local character = LocalPlayer.Character
            if character and character:FindFirstChild("HumanoidRootPart") then
                character.HumanoidRootPart.CFrame = targetHRP.CFrame
                self:DisplayNotification("Teleported to " .. username)
            else
                self:DisplayNotification("Your character is not available", 3)
            end
        else
            self:DisplayNotification("Player not found or unavailable", 3)
        end
    end)
end

-- Display notification function
function RubyHub:DisplayNotification(message, duration)
    duration = duration or 2.5

    -- Create notification frame
    local notification = CreateInstance("Frame", {
        Name = "Notification",
        BackgroundColor3 = SIDEBAR_COLOR,
        BorderSizePixel = 0,
        Position = UDim2.new(0.5, -100, 0, -40),
        Size = UDim2.new(0, 200, 0, 40),
        Parent = self.GUI
    })

    SimpleCorner(notification)

    local notificationText = CreateInstance("TextLabel", {
        Name = "Text",
        BackgroundTransparency = 1,
        Position = UDim2.new(0, 10, 0, 0),
        Size = UDim2.new(1, -20, 1, 0),
        Font = Enum.Font.GothamSemibold,
        Text = message,
        TextColor3 = TEXT_COLOR,
        TextSize = 14,
        TextWrapped = true,
        Parent = notification
    })

    -- Animate notification
    pcall(function()
        notification.Position = UDim2.new(0.5, -100, 0, -40)
        notification:TweenPosition(UDim2.new(0.5, -100, 0, 20), Enum.EasingDirection.Out, Enum.EasingStyle.Quint, 0.5)

        delay(duration, function()
            pcall(function()
                notification:TweenPosition(UDim2.new(0.5, -100, 0, -40), Enum.EasingDirection.In, Enum.EasingStyle.Quint, 0.5)

                delay(0.5, function()
                    pcall(function()
                        notification:Destroy()
                    end)
                end)
            end)
        end)
    end)
end

-- Implementation of the Fly feature
function RubyHub:ImplementFly()
    pcall(function()
        local character = LocalPlayer.Character
        if not character or not character:FindFirstChild("HumanoidRootPart") then return end

        local hrp = character:FindFirstChild("HumanoidRootPart")
        local humanoid = character:FindFirstChildOfClass("Humanoid")

        if self.Flying then
            -- Create BodyVelocity for flight
            if not hrp:FindFirstChild("FlyForce") then
                local flyForce = Instance.new("BodyVelocity")
                flyForce.Name = "FlyForce"
                flyForce.MaxForce = Vector3.new(math.huge, math.huge, math.huge)
                flyForce.Velocity = Vector3.zero
                flyForce.Parent = hrp
            end

            -- Create BodyGyro for stabilization
            if not hrp:FindFirstChild("FlyGyro") then
                local flyGyro = Instance.new("BodyGyro")
                flyGyro.Name = "FlyGyro"
                flyGyro.MaxTorque = Vector3.new(math.huge, math.huge, math.huge)
                flyGyro.CFrame = hrp.CFrame
                flyGyro.Parent = hrp
            end

            -- Disable humanoid animations to make the character stiff
            if humanoid then
                humanoid:ChangeState(Enum.HumanoidStateType.Physics)
            end

            -- Connect controls for flying
            if not self.FlyConnection then
                self.FlyConnection = RunService.RenderStepped:Connect(function()
                    if not self.Flying then
                        if self.FlyConnection then
                            self.FlyConnection:Disconnect()
                            self.FlyConnection = nil
                        end
                        return
                    end

                    local flyForce = hrp:FindFirstChild("FlyForce")
                    local flyGyro = hrp:FindFirstChild("FlyGyro")

                    if flyForce and flyGyro then
                        flyGyro.CFrame = Camera.CFrame

                        local velocity = Vector3.zero

                        -- Forward/Backward
                        if UserInputService:IsKeyDown(Enum.KeyCode.W) then
                            velocity = velocity + (Camera.CFrame.LookVector * self.FlySpeed)
                        end
                        if UserInputService:IsKeyDown(Enum.KeyCode.S) then
                            velocity = velocity - (Camera.CFrame.LookVector * self.FlySpeed)
                        end

                        -- Left/Right
                        if UserInputService:IsKeyDown(Enum.KeyCode.A) then
                            velocity = velocity - (Camera.CFrame.RightVector * self.FlySpeed)
                        end
                        if UserInputService:IsKeyDown(Enum.KeyCode.D) then
                            velocity = velocity + (Camera.CFrame.RightVector * self.FlySpeed)
                        end

                        -- Up/Down
                        if UserInputService:IsKeyDown(Enum.KeyCode.Space) then
                            velocity = velocity + (Camera.CFrame.UpVector * self.FlySpeed)
                        end
                        if UserInputService:IsKeyDown(Enum.KeyCode.LeftShift) then
                            velocity = velocity - (Camera.CFrame.UpVector * self.FlySpeed)
                        end

                        -- Apply velocity to the character
                        flyForce.Velocity = velocity
                    end
                end)
            end
        else
            -- Remove flight objects
            if hrp:FindFirstChild("FlyForce") then
                hrp.FlyForce:Destroy()
            end

            if hrp:FindFirstChild("FlyGyro") then
                hrp.FlyGyro:Destroy()
            end

            -- Re-enable humanoid animations
            if humanoid then
                humanoid:ChangeState(Enum.HumanoidStateType.GettingUp)
            end

            -- Disconnect flying connection
            if self.FlyConnection then
                self.FlyConnection:Disconnect()
                self.FlyConnection = nil
            end
        end
    end)
end

-- Add keybind to toggle flying with debounce to prevent input spam
local flyDebounce = false
local lastFlyToggleTime = 0
local aimbotDebounce = false
local lastAimbotToggleTime = 0

UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end

    local currentTime = tick()

    -- Flying toggle with Q key
    if input.KeyCode == Enum.KeyCode.Q and not flyDebounce then
        if currentTime - lastFlyToggleTime < 0.3 then return end

        lastFlyToggleTime = currentTime
        flyDebounce = true

        RubyHub.Flying = not RubyHub.Flying
        RubyHub:ImplementFly()

        spawn(function()
            wait(0.1)
            flyDebounce = false
        end)
    end

    -- Aimbot toggle with O key
    if input.KeyCode == Enum.KeyCode.O and not aimbotDebounce then
        if currentTime - lastAimbotToggleTime < 0.3 then return end

        lastAimbotToggleTime = currentTime
        aimbotDebounce = true

        RubyHub.AimBot = not RubyHub.AimBot
        RubyHub:ImplementAimbot()

        -- Display notification about aimbot state
        if RubyHub.AimBot then
            RubyHub:DisplayNotification("Aimbot Enabled")
        else
            RubyHub:DisplayNotification("Aimbot Disabled")
        end

        spawn(function()
            wait(0.1)
            aimbotDebounce = false
        end)
    end
end)

-- Implementation of NoClip feature
function RubyHub:ImplementNoClip()
    pcall(function()
        local character = LocalPlayer.Character
        if not character then return end

        if self.NoClip then
            if not self.NoClipConnection then
                self.NoClipConnection = RunService.Stepped:Connect(function()
                    if not self.NoClip then
                        if self.NoClipConnection then
                            self.NoClipConnection:Disconnect()
                            self.NoClipConnection = nil
                        end
                        return
                    end

                    for _, part in pairs(character:GetDescendants()) do
                        if part:IsA("BasePart") and part.CanCollide then
                            part.CanCollide = false
                        end
                    end
                end)
            end
        else
            if self.NoClipConnection then
                self.NoClipConnection:Disconnect()
                self.NoClipConnection = nil
            end

            for _, part in pairs(character:GetDescendants()) do
                if part:IsA("BasePart") and part.Name ~= "HumanoidRootPart" then
                    part.CanCollide = true
                end
            end
        end
    end)
end

-- Function to check if a player is on the same team
function RubyHub:IsTeamMember(player)
    -- If team check is disabled, treat everyone as enemies
    if not self.TeamCheck then return false end

    -- Handle nil player
    if not player then return false end

    -- Check using standard team properties
    if player.Team and LocalPlayer.Team then
        return player.Team == LocalPlayer.Team
    end

    -- Check using TeamColor
    if player.TeamColor and LocalPlayer.TeamColor then
        return player.TeamColor == LocalPlayer.TeamColor
    end

    -- Try to check torso colors as last resort
    pcall(function()
        if player.Character and LocalPlayer.Character then
            local playerTorso = player.Character:FindFirstChild("Torso") or player.Character:FindFirstChild("UpperTorso")
            local localTorso = LocalPlayer.Character:FindFirstChild("Torso") or LocalPlayer.Character:FindFirstChild("UpperTorso")

            if playerTorso and localTorso and playerTorso.Color and localTorso.Color then
                return playerTorso.Color == localTorso.Color
            end
        end
    end)

    return false
end

-- Implementation of the Silent Aim feature
function RubyHub:ImplementSilentAim()
    pcall(function()
        if self.SilentAim then
            -- Create aim circle around cursor
            if not self.AimCircle then
                self.AimCircle = self.Drawing.new("Circle")
                self.AimCircle.Visible = true
                self.AimCircle.Radius = 80
                self.AimCircle.Thickness = 1
                self.AimCircle.Color = Color3.fromRGB(255, 255, 255)
                self.AimCircle.Filled = false
                self.AimCircle.Transparency = 1
            end

            -- Create the ESP lines for all players
            for _, player in pairs(Players:GetPlayers()) do
                if player ~= LocalPlayer and not self:IsTeamMember(player) and player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
                    if not self.ESPObjects[player] then
                        self.ESPObjects[player] = {
                            Line = self.Drawing.new("Line"),
                            Highlight = Instance.new("Highlight")
                        }

                        -- Configure line
                        if self.ESPObjects[player].Line then
                            self.ESPObjects[player].Line.Visible = true
                            self.ESPObjects[player].Line.Thickness = 1
                            self.ESPObjects[player].Line.Color = Color3.fromRGB(255, 0, 0)
                        end

                        -- Configure highlight
                        if self.ESPObjects[player].Highlight then
                            self.ESPObjects[player].Highlight.FillColor = Color3.fromRGB(255, 0, 0)
                            self.ESPObjects[player].Highlight.OutlineColor = Color3.fromRGB(255, 0, 0)
                            self.ESPObjects[player].Highlight.FillTransparency = 0.7
                            self.ESPObjects[player].Highlight.OutlineTransparency = 0

                            if player.Character then
                                self.ESPObjects[player].Highlight.Parent = player.Character
                            end
                        end
                    end
                end
            end

            -- Update the aim circle with cursor position
            if not self.SilentAimConnection then
                self.SilentAimConnection = RunService.RenderStepped:Connect(function()
                    if not self.SilentAim then
                        if self.SilentAimConnection then
                            self.SilentAimConnection:Disconnect()
                            self.SilentAimConnection = nil
                        end
                        return
                    end

                    -- Update aim circle position to match mouse
                    local mousePos = UserInputService:GetMouseLocation()
                    if self.AimCircle then
                        self.AimCircle.Position = mousePos
                    end

                    -- Update objects for any new players
                    for _, player in pairs(Players:GetPlayers()) do
                        if player ~= LocalPlayer and not self:IsTeamMember(player) and player.Character and not self.ESPObjects[player] then
                            self.ESPObjects[player] = {
                                Line = self.Drawing.new("Line"),
                                Highlight = Instance.new("Highlight")
                            }

                            -- Configure line
                            if self.ESPObjects[player].Line then
                                self.ESPObjects[player].Line.Visible = true
                                self.ESPObjects[player].Line.Thickness = 1
                                self.ESPObjects[player].Line.Color = Color3.fromRGB(255, 0, 0)
                            end

                            -- Configure highlight
                            if self.ESPObjects[player].Highlight then
                                self.ESPObjects[player].Highlight.FillColor = Color3.fromRGB(255, 0, 0)
                                self.ESPObjects[player].Highlight.OutlineColor = Color3.fromRGB(255, 0, 0)
                                self.ESPObjects[player].Highlight.FillTransparency = 0.7
                                self.ESPObjects[player].Highlight.OutlineTransparency = 0

                                if player.Character then
                                    self.ESPObjects[player].Highlight.Parent = player.Character
                                end
                            end
                        end
                    end

                    -- Update ESP lines
                    local closestPlayer = nil
                    local closestDist = math.huge

                    for player, obj in pairs(self.ESPObjects) do
                        -- Check if player still exists and is valid
                        if player and typeof(player) == "Instance" and player:IsA("Player") and player.Parent and player.Character and
                           player.Character:FindFirstChild("HumanoidRootPart") and
                           player.Character:FindFirstChildOfClass("Humanoid") and
                           player.Character:FindFirstChildOfClass("Humanoid").Health > 0 then

                            -- Skip if team check is enabled and player is a team member
                            if self.TeamCheck and self:IsTeamMember(player) then
                                if obj.Line then obj.Line.Visible = false end
                                if obj.Highlight then obj.Highlight.Enabled = false end
                            else
                                if obj.Highlight then obj.Highlight.Enabled = true end

                                -- Calculate screen position of player
                                local hrp = player.Character.HumanoidRootPart
                                local screenPos, onScreen = Camera:WorldToScreenPoint(hrp.Position)

                                if onScreen and obj.Line then
                                    obj.Line.From = mousePos
                                    obj.Line.To = Vector2.new(screenPos.X, screenPos.Y)
                                    obj.Line.Visible = true

                                    -- Calculate distance to player
                                    local dist = (Vector2.new(screenPos.X, screenPos.Y) - mousePos).Magnitude

                                    -- Check if this player is closer than previous closest
                                    if dist < closestDist and self.AimCircle and dist <= self.AimCircle.Radius then
                                        closestDist = dist
                                        closestPlayer = player
                                    end
                                elseif obj.Line then
                                    obj.Line.Visible = false
                                end
                            end
                        else
                            -- Remove invalid player references
                            if obj.Line then obj.Line:Remove() end
                            if obj.Highlight then obj.Highlight:Destroy() end
                            self.ESPObjects[player] = nil
                        end
                    end

                    -- Highlight closest player
                    for player, obj in pairs(self.ESPObjects) do
                        if player and obj.Line then
                            if player == closestPlayer then
                                obj.Line.Thickness = 2
                                obj.Line.Color = Color3.fromRGB(255, 255, 0)

                                -- Lock aim to closest player when right mouse button is held
                                if UserInputService:IsMouseButtonPressed(Enum.UserInputType.MouseButton2) and
                                   player.Character and player.Character:FindFirstChild("Head") then
                                    local head = player.Character.Head
                                    Camera.CFrame = CFrame.new(Camera.CFrame.Position, head.Position)
                                end
                            else
                                obj.Line.Thickness = 1
                                obj.Line.Color = Color3.fromRGB(255, 0, 0)
                            end
                        end
                    end
                end)
            end
        else
            -- Clean up silent aim objects
            if self.AimCircle then
                pcall(function()
                    self.AimCircle.Visible = false
                    self.AimCircle:Remove()
                end)
                self.AimCircle = nil
            end

            if self.SilentAimConnection then
                pcall(function() self.SilentAimConnection:Disconnect() end)
                self.SilentAimConnection = nil
            end

            -- Thoroughly clean up all Silent Aim lines and highlights
            for player, obj in pairs(self.ESPObjects) do
                -- First make everything invisible
                if obj.Line then
                    pcall(function()
                        obj.Line.Visible = false
                        obj.Line:Remove()
                    end)
                end

                if obj.Highlight then
                    pcall(function()
                        obj.Highlight.Enabled = false
                        obj.Highlight:Destroy()
                    end)
                end

                -- Remove the reference
                self.ESPObjects[player] = nil
            end

            -- Clear the table to ensure no references remain
            table.clear(self.ESPObjects)
        end
    end)
end

-- Implementation of the Aimbot feature
function RubyHub:ImplementAimbot()
    pcall(function()
        if self.AimBot then
            if not self.AimbotConnection then
                self.AimbotConnection = RunService.RenderStepped:Connect(function()
                    if not self.AimBot then
                        if self.AimbotConnection then
                            self.AimbotConnection:Disconnect()
                            self.AimbotConnection = nil
                        end
                        return
                    end

                    local closestPlayer = nil
                    local closestDist = math.huge

                    -- Find closest visible player
                    for _, player in pairs(Players:GetPlayers()) do
                        if self.TeamCheck and self:IsTeamMember(player) then
                            continue
                        end

                        if player ~= LocalPlayer and player.Character and
                           player.Character:FindFirstChildOfClass("Humanoid") and
                           player.Character:FindFirstChildOfClass("Humanoid").Health > 0 then

                            -- Handle different character rigs (R6/R15)
                            local targetPart

                            if self.TargetBone == "Torso" and not player.Character:FindFirstChild("Torso") and player.Character:FindFirstChild("UpperTorso") then
                                -- Handle R15 rig when targeting Torso
                                targetPart = player.Character:FindFirstChild("UpperTorso")
                            elseif self.TargetBone == "UpperTorso" and not player.Character:FindFirstChild("UpperTorso") and player.Character:FindFirstChild("Torso") then
                                -- Handle R6 rig when targeting UpperTorso
                                targetPart = player.Character:FindFirstChild("Torso")
                            else
                                -- Normal case
                                targetPart = player.Character:FindFirstChild(self.TargetBone)
                            end

                            -- Skip if target part doesn't exist
                            if not targetPart then
                                continue
                            end

                            -- Perform raycasting to check visibility
                            local ray = Ray.new(Camera.CFrame.Position, (targetPart.Position - Camera.CFrame.Position).Unit * 1000)
                            local part, position = workspace:FindPartOnRayWithIgnoreList(ray, {LocalPlayer.Character, Camera})
                            local iterationCount = 0 -- Safeguard to prevent infinite loops

                            -- Ignore transparent or non-collidable parts
                            while part and (part.Transparency > 0.5 or not part.CanCollide) and iterationCount < 10 do
                                ray = Ray.new(position, (targetPart.Position - position).Unit * 1000)
                                part, position = workspace:FindPartOnRayWithIgnoreList(ray, {LocalPlayer.Character, Camera})
                                iterationCount += 1
                            end

                            if part and part:IsDescendantOf(player.Character) then
                                local screenPos, onScreen = Camera:WorldToScreenPoint(targetPart.Position)

                                if onScreen then
                                    local mousePos = UserInputService:GetMouseLocation()
                                    local dist = (Vector2.new(screenPos.X, screenPos.Y) - mousePos).Magnitude

                                    if dist < closestDist then
                                        closestDist = dist
                                        closestPlayer = player
                                    end
                                end
                            end
                        end
                    end

                    -- Aim at closest visible player
                    if closestPlayer and closestPlayer.Character then
                        -- Handle different character rigs (R6/R15)
                        local targetPart

                        if self.TargetBone == "Torso" and not closestPlayer.Character:FindFirstChild("Torso") and closestPlayer.Character:FindFirstChild("UpperTorso") then
                            -- Handle R15 rig when targeting Torso
                            targetPart = closestPlayer.Character:FindFirstChild("UpperTorso")
                        elseif self.TargetBone == "UpperTorso" and not closestPlayer.Character:FindFirstChild("UpperTorso") and closestPlayer.Character:FindFirstChild("Torso") then
                            -- Handle R6 rig when targeting UpperTorso
                            targetPart = closestPlayer.Character:FindFirstChild("Torso")
                        else
                            -- Normal case
                            targetPart = closestPlayer.Character:FindFirstChild(self.TargetBone)
                        end

                        if targetPart then
                            self.AimbotTarget = closestPlayer
                            Camera.CFrame = CFrame.new(Camera.CFrame.Position, targetPart.Position)
                        else
                            self.AimbotTarget = nil
                        end
                    else
                        self.AimbotTarget = nil
                    end
                end)
            end
        else
            if self.AimbotConnection then
                pcall(function() self.AimbotConnection:Disconnect() end)
                self.AimbotConnection = nil
            end
            self.AimbotTarget = nil
        end
    end)
end

-- Implementation of the Spazz Aimbot feature
function RubyHub:ImplementSpazzAimbot()
    pcall(function()
        local character = LocalPlayer.Character
        if not character or not character:FindFirstChild("HumanoidRootPart") then return end

        local hrp = character:FindFirstChild("HumanoidRootPart")

        if self.SpazzAimbot then
            if not self.SpazzAimbotConnection then
                -- Prepare velocity multipliers for erratic movement
                self.SpazzVelocityX = 1
                self.SpazzVelocityY = 1
                self.SpazzVelocityZ = 1
                self.SpazzRotationSpeed = 15
                self.SpazzLastUpdate = tick()

                -- Create gyro for spinning
                if not hrp:FindFirstChild("SpazzGyro") then
                    local spazzGyro = Instance.new("BodyGyro")
                    spazzGyro.Name = "SpazzGyro"
                    spazzGyro.MaxTorque = Vector3.new(math.huge, math.huge, math.huge)
                    spazzGyro.P = 10000
                    spazzGyro.D = 100
                    spazzGyro.CFrame = hrp.CFrame
                    spazzGyro.Parent = hrp
                end

                -- Create velocity for erratic movement
                if not hrp:FindFirstChild("SpazzVelocity") then
                    local spazzVelocity = Instance.new("BodyVelocity")
                    spazzVelocity.Name = "SpazzVelocity"
                    spazzVelocity.MaxForce = Vector3.new(50000, 50000, 50000)
                    spazzVelocity.Velocity = Vector3.new(0, 0, 0)
                    spazzVelocity.P = 1250
                    spazzVelocity.Parent = hrp
                end

                -- Connect spazz behavior
                self.SpazzAimbotConnection = RunService.Heartbeat:Connect(function()
                    if not self.SpazzAimbot then
                        if self.SpazzAimbotConnection then
                            self.SpazzAimbotConnection:Disconnect()
                            self.SpazzAimbotConnection = nil
                        end
                        return
                    end

                    if not character or not character:FindFirstChild("HumanoidRootPart") then
                        return
                    end

                    local hrp = character:FindFirstChild("HumanoidRootPart")
                    local spazzGyro = hrp:FindFirstChild("SpazzGyro")
                    local spazzVelocity = hrp:FindFirstChild("SpazzVelocity")

                    if spazzGyro and spazzVelocity then
                        -- Change direction vectors randomly but smoothly
                        local currentTime = tick()
                        local deltaTime = currentTime - self.SpazzLastUpdate
                        self.SpazzLastUpdate = currentTime

                        -- Oscillate velocity multipliers with noise for unpredictability
                        self.SpazzVelocityX = math.sin(currentTime * 3.7) * math.cos(currentTime * 2.3) * 5
                        self.SpazzVelocityY = math.cos(currentTime * 4.2) * math.sin(currentTime * 1.8) * 5
                        self.SpazzVelocityZ = math.sin(currentTime * 2.8) * math.cos(currentTime * 3.5) * 5

                        -- Calculate eased rotation for more organic movement
                        local yawAngle = (math.sin(currentTime * self.SpazzRotationSpeed) * 180) + (math.cos(currentTime * 2.7) * 60)
                        local pitchAngle = (math.cos(currentTime * 3.2) * 45) + (math.sin(currentTime * 1.4) * 25)
                        local rollAngle = (math.sin(currentTime * 2.5) * 30) + (math.cos(currentTime * 4.1) * 15)

                        -- Apply rotation
                        pcall(function()
                            spazzGyro.CFrame = CFrame.new(hrp.Position) *
                                CFrame.fromEulerAnglesXYZ(
                                    math.rad(pitchAngle),
                                    math.rad(yawAngle),
                                    math.rad(rollAngle)
                                )
                        end)

                        -- Generate erratic movement (but controlled enough to not break aim)
                        local velocityVector = Vector3.new(
                            self.SpazzVelocityX,
                            self.SpazzVelocityY,
                            self.SpazzVelocityZ
                        )

                        -- Scale velocity for better control
                        pcall(function() spazzVelocity.Velocity = velocityVector * 8 end)

                        -- Preserve aimbot functionality even during spazzing
                        if self.AimBot and self.AimbotTarget and self.AimbotTarget.Character and self.AimbotTarget.Character:FindFirstChild(self.TargetBone) then
                            local targetPart = self.AimbotTarget.Character:FindFirstChild(self.TargetBone)
                            Camera.CFrame = CFrame.new(Camera.CFrame.Position, targetPart.Position)
                        end
                    end
                end)
            end
        else
            -- Clean up spazz objects
            if self.SpazzAimbotConnection then
                pcall(function() self.SpazzAimbotConnection:Disconnect() end)
                self.SpazzAimbotConnection = nil
            end

            local hrp = character:FindFirstChild("HumanoidRootPart")
            if hrp then
                if hrp:FindFirstChild("SpazzGyro") then
                    pcall(function() hrp.SpazzGyro:Destroy() end)
                end

                if hrp:FindFirstChild("SpazzVelocity") then
                    pcall(function() hrp.SpazzVelocity:Destroy() end)
                end
            end
        end
    end)
end

-- Implementation of ESP feature
function RubyHub:ImplementESP()
    pcall(function()
        if self.ESP then
            -- Create ESP objects for all players
            for _, player in pairs(Players:GetPlayers()) do
                if player ~= LocalPlayer then
                    if not self.ESPObjects[player] then
                        self.ESPObjects[player] = {
                            Box = self.Drawing.new("Square"),
                            Name = self.Drawing.new("Text"),
                            Distance = self.Drawing.new("Text"),
                            Tracer = self.Drawing.new("Line"),
                            Highlight = Instance.new("Highlight")
                        }

                        -- Box configuration
                        if self.ESPObjects[player].Box then
                            self.ESPObjects[player].Box.Visible = true
                            self.ESPObjects[player].Box.Color = Color3.fromRGB(255, 0, 0)
                            self.ESPObjects[player].Box.Thickness = 1
                            self.ESPObjects[player].Box.Filled = false
                            self.ESPObjects[player].Box.Transparency = 1
                        end

                        -- Name configuration
                        if self.ESPObjects[player].Name then
                            self.ESPObjects[player].Name.Visible = true
                            self.ESPObjects[player].Name.Color = Color3.fromRGB(255, 255, 255)
                            self.ESPObjects[player].Name.Size = 14
                            self.ESPObjects[player].Name.Center = true
                            self.ESPObjects[player].Name.Outline = true
                            self.ESPObjects[player].Name.OutlineColor = Color3.fromRGB(0, 0, 0)
                            self.ESPObjects[player].Name.Text = player.Name
                        end

                        -- Distance configuration
                        if self.ESPObjects[player].Distance then
                            self.ESPObjects[player].Distance.Visible = true
                            self.ESPObjects[player].Distance.Color = Color3.fromRGB(255, 255, 255)
                            self.ESPObjects[player].Distance.Size = 14
                            self.ESPObjects[player].Distance.Center = true
                            self.ESPObjects[player].Distance.Outline = true
                            self.ESPObjects[player].Distance.OutlineColor = Color3.fromRGB(0, 0, 0)
                        end

                        -- Tracer configuration
                        if self.ESPObjects[player].Tracer then
                            self.ESPObjects[player].Tracer.Visible = true
                            self.ESPObjects[player].Tracer.Color = Color3.fromRGB(255, 0, 0)
                            self.ESPObjects[player].Tracer.Thickness = 1
                            self.ESPObjects[player].Tracer.Transparency = 1
                        end

                        -- Highlight configuration
                        if self.ESPObjects[player].Highlight then
                            self.ESPObjects[player].Highlight.FillColor = Color3.fromRGB(255, 0, 0)
                            self.ESPObjects[player].Highlight.OutlineColor = Color3.fromRGB(255, 255, 255)
                            self.ESPObjects[player].Highlight.FillTransparency = 0.7
                            self.ESPObjects[player].Highlight.OutlineTransparency = 0

                            if player.Character then
                                self.ESPObjects[player].Highlight.Parent = player.Character
                            end
                        end
                    end

                    -- Handle player character changes
                    pcall(function()
                        player.CharacterAdded:Connect(function(character)
                            if self.ESPObjects[player] and self.ESP and self.ESPObjects[player].Highlight then
                                self.ESPObjects[player].Highlight.Parent = character
                            end
                        end)
                    end)
                end
            end

            -- Handle player joining
            pcall(function()
                Players.PlayerAdded:Connect(function(player)
                    if player ~= LocalPlayer and self.ESP then
                        self.ESPObjects[player] = {
                            Box = self.Drawing.new("Square"),
                            Name = self.Drawing.new("Text"),
                            Distance = self.Drawing.new("Text"),
                            Tracer = self.Drawing.new("Line"),
                            Highlight = Instance.new("Highlight")
                        }

                        -- Configure ESP objects (like above)

                        player.CharacterAdded:Connect(function(character)
                            if self.ESPObjects[player] and self.ESP and self.ESPObjects[player].Highlight then
                                self.ESPObjects[player].Highlight.Parent = character

                                -- Add humanoid died event to clean up ESP immediately when player is killed
                                local humanoid = character:FindFirstChildOfClass("Humanoid")
                                if humanoid then
                                    humanoid.Died:Connect(function()
                                        if self.ESPObjects[player] then
                                            -- Hide all ESP elements immediately
                                            if self.ESPObjects[player].Box then self.ESPObjects[player].Box.Visible = false end
                                            if self.ESPObjects[player].Name then self.ESPObjects[player].Name.Visible = false end
                                            if self.ESPObjects[player].Distance then self.ESPObjects[player].Distance.Visible = false end
                                            if self.ESPObjects[player].Tracer then self.ESPObjects[player].Tracer.Visible = false end
                                            if self.ESPObjects[player].Line then self.ESPObjects[player].Line.Visible = false end
                                            if self.ESPObjects[player].Highlight then self.ESPObjects[player].Highlight.Enabled = false end
                                        end
                                    end)
                                end
                            end
                        end)
                    end
                end)

                -- Handle player leaving
                Players.PlayerRemoving:Connect(function(player)
                    if self.ESPObjects[player] then
                        -- Clean up ESP objects for the player
                        if self.ESPObjects[player].Box then
                            pcall(function()
                                self.ESPObjects[player].Box.Visible = false
                                self.ESPObjects[player].Box:Remove()
                            end)
                        end

                        if self.ESPObjects[player].Name then
                            pcall(function()
                                self.ESPObjects[player].Name.Visible = false
                                self.ESPObjects[player].Name:Remove()
                            end)
                        end

                        if self.ESPObjects[player].Distance then
                            pcall(function()
                                self.ESPObjects[player].Distance.Visible = false
                                self.ESPObjects[player].Distance:Remove()
                            end)
                        end

                        if self.ESPObjects[player].Tracer then
                            pcall(function()
                                self.ESPObjects[player].Tracer.Visible = false
                                self.ESPObjects[player].Tracer:Remove()
                            end)
                        end

                        if self.ESPObjects[player].Line then
                            pcall(function()
                                self.ESPObjects[player].Line.Visible = false
                                self.ESPObjects[player].Line:Remove()
                            end)
                        end

                        if self.ESPObjects[player].Highlight then
                            pcall(function()
                                self.ESPObjects[player].Highlight.Enabled = false
                                self.ESPObjects[player].Highlight:Destroy()
                            end)
                        end

                        -- Remove the player from the ESP objects table
                        self.ESPObjects[player] = nil
                    end
                end)
            end)

            -- Update ESP objects
            if not self.ESPConnection then
                self.ESPConnection = RunService.RenderStepped:Connect(function()
                    if not self.ESP then
                        if self.ESPConnection then
                            self.ESPConnection:Disconnect()
                            self.ESPConnection = nil
                        end
                        return
                    end

                    for player, obj in pairs(self.ESPObjects) do
                        -- Skip if team check is enabled and player is a team member
                        if self.TeamCheck and self:IsTeamMember(player) then
                            if obj.Box then obj.Box.Visible = false end
                            if obj.Name then obj.Name.Visible = false end
                            if obj.Distance then obj.Distance.Visible = false end
                            if obj.Tracer then obj.Tracer.Visible = false end
                            if obj.Highlight then obj.Highlight.Enabled = false end
                            continue
                        else
                            if obj.Highlight then obj.Highlight.Enabled = true end
                        end

                        -- Check if player exists and is valid
                        if player and typeof(player) == "Instance" and player:IsA("Player") and player.Character then
                            -- Get humanoid and check health
                            local humanoid = player.Character:FindFirstChildOfClass("Humanoid")

                            -- Immediately hide ESP if player is dead or has no humanoid
                            if not humanoid or humanoid.Health <= 0 then
                                -- Hide all ESP elements immediately
                                if obj.Box then obj.Box.Visible = false end
                                if obj.Name then obj.Name.Visible = false end
                                if obj.Distance then obj.Distance.Visible = false end
                                if obj.Tracer then obj.Tracer.Visible = false end
                                if obj.Line then obj.Line.Visible = false end
                                if obj.Highlight then obj.Highlight.Enabled = false end
                                continue
                            end

                            -- Continue only if player has a HumanoidRootPart
                            if player.Character:FindFirstChild("HumanoidRootPart") then
                                local hrp = player.Character.HumanoidRootPart
                                local head = player.Character:FindFirstChild("Head")

                                if not head then
                                    head = hrp
                                end

                                local rootPos = hrp.Position
                                local headPos = head.Position + Vector3.new(0, 1, 0)

                                local rootPoint = Camera:WorldToScreenPoint(rootPos)
                                local headPoint = Camera:WorldToScreenPoint(headPos)

                                if rootPoint.Z > 0 then
                                    -- Calculate box size based on character height
                                    local boxSize = headPoint.Y - rootPoint.Y
                                    local boxWidth = boxSize / 2

                                    -- Update box
                                    if obj.Box then
                                        obj.Box.Size = Vector2.new(boxWidth, boxSize)
                                        obj.Box.Position = Vector2.new(rootPoint.X - boxWidth / 2, rootPoint.Y)
                                        obj.Box.Visible = true
                                    end

                                    -- Update name
                                    if obj.Name then
                                        obj.Name.Position = Vector2.new(rootPoint.X, rootPoint.Y - boxSize - 16)
                                        obj.Name.Visible = true
                                    end

                                    -- Update distance
                                    if obj.Distance and LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
                                        local distance = (LocalPlayer.Character.HumanoidRootPart.Position - rootPos).Magnitude
                                        obj.Distance.Text = math.floor(distance) .. "m"
                                        obj.Distance.Position = Vector2.new(rootPoint.X, rootPoint.Y + 10)
                                        obj.Distance.Visible = true
                                    end

                                    -- Update tracer
                                    if obj.Tracer and Camera.ViewportSize then
                                        obj.Tracer.From = Vector2.new(Camera.ViewportSize.X / 2, Camera.ViewportSize.Y)
                                        obj.Tracer.To = Vector2.new(rootPoint.X, rootPoint.Y)
                                        obj.Tracer.Visible = true
                                    end

                                    -- Update health-based color
                                    local humanoid = player.Character:FindFirstChildOfClass("Humanoid")
                                    if humanoid then
                                        local health = humanoid.Health / humanoid.MaxHealth

                                        local r = math.floor(255 * (1 - health))
                                        local g = math.floor(255 * health)

                                        local color = Color3.fromRGB(r, g, 0)

                                        if obj.Box then obj.Box.Color = color end
                                        if obj.Tracer then obj.Tracer.Color = color end
                                        if obj.Highlight then obj.Highlight.FillColor = color end
                                    end
                                else
                                    -- Hide ESP elements when player is behind camera
                                    if obj.Box then obj.Box.Visible = false end
                                    if obj.Name then obj.Name.Visible = false end
                                    if obj.Distance then obj.Distance.Visible = false end
                                    if obj.Tracer then obj.Tracer.Visible = false end
                                end
                            end
                        else
                            -- Hide ESP elements if player character doesn't exist or has no HumanoidRootPart
                            if obj.Box then obj.Box.Visible = false end
                            if obj.Name then obj.Name.Visible = false end
                            if obj.Distance then obj.Distance.Visible = false end
                            if obj.Tracer then obj.Tracer.Visible = false end
                            if obj.Line then obj.Line.Visible = false end
                            if obj.Highlight then obj.Highlight.Enabled = false end
                        end
                    end
                end)
            end
        else
            -- Clean up ESP objects
            if self.ESPConnection then
                pcall(function() self.ESPConnection:Disconnect() end)
                self.ESPConnection = nil
            end

            -- Thoroughly clean up all ESP objects
            for player, obj in pairs(self.ESPObjects) do
                -- Remove all drawing objects
                if obj.Box then
                    pcall(function()
                        obj.Box.Visible = false
                        obj.Box:Remove()
                    end)
                end

                if obj.Name then
                    pcall(function()
                        obj.Name.Visible = false
                        obj.Name:Remove()
                    end)
                end

                if obj.Distance then
                    pcall(function()
                        obj.Distance.Visible = false
                        obj.Distance:Remove()
                    end)
                end

                if obj.Tracer then
                    pcall(function()
                        obj.Tracer.Visible = false
                        obj.Tracer:Remove()
                    end)
                end

                if obj.Line then
                    pcall(function()
                        obj.Line.Visible = false
                        obj.Line:Remove()
                    end)
                end

                -- Remove highlight
                if obj.Highlight then
                    pcall(function()
                        obj.Highlight.Enabled = false
                        obj.Highlight:Destroy()
                    end)
                end

                -- Clear the reference
                self.ESPObjects[player] = nil
            end

            -- Clear the entire table to ensure no references remain
            table.clear(self.ESPObjects)
        end
    end)
end

-- Implementation of Infinite Jump feature
function RubyHub:ImplementInfiniteJump()
    pcall(function()
        if self.InfiniteJump then
            if not self.InfiniteJumpConnection then
                self.InfiniteJumpConnection = UserInputService.JumpRequest:Connect(function()
                    if not self.InfiniteJump then
                        if self.InfiniteJumpConnection then
                            self.InfiniteJumpConnection:Disconnect()
                            self.InfiniteJumpConnection = nil
                        end
                        return
                    end

                    local character = LocalPlayer.Character
                    if character and character:FindFirstChildOfClass("Humanoid") then
                        pcall(function()
                            character:FindFirstChildOfClass("Humanoid"):ChangeState(Enum.HumanoidStateType.Jumping)
                        end)
                    end
                end)
            end
        else
            if self.InfiniteJumpConnection then
                pcall(function() self.InfiniteJumpConnection:Disconnect() end)
                self.InfiniteJumpConnection = nil
            end
        end
    end)
end

-- Implementation of Anti AFK feature
function RubyHub:ImplementAntiAFK()
    pcall(function()
        if self.AntiAFK then
            if not self.AntiAFKConnection then
                self.AntiAFKConnection = true

                -- Simple anti-AFK approach
                spawn(function()
                    while self.AntiAFK do
                        -- Simulate player activity
                        pcall(function()
                            local virtualUser = game:GetService("VirtualUser")
                            virtualUser:CaptureController()
                            virtualUser:ClickButton2(Vector2.new())
                        end)

                        -- Wait before next activity
                        wait(300) -- 5 minutes
                    end
                end)

                -- Additional hook to prevent AFK kicks
                pcall(function()
                    if LocalPlayer and LocalPlayer.Idled then
                        LocalPlayer.Idled:Connect(function()
                            if self.AntiAFK then
                                pcall(function()
                                    local virtualUser = game:GetService("VirtualUser")
                                    virtualUser:CaptureController()
                                    virtualUser:ClickButton2(Vector2.new())
                                end)
                            end
                        end)
                    end
                end)
            end
        else
            self.AntiAFKConnection = false
        end
    end)
end

-- Implementation of Hitbox Expander
function RubyHub:ImplementHitboxExpander()
    pcall(function()
        if self.HitboxExpander then
            for _, player in pairs(Players:GetPlayers()) do
                if player ~= LocalPlayer then
                    local function applyHitbox(character)
                        local hrp = character:FindFirstChild("HumanoidRootPart")
                        if hrp then
                            hrp.Size = Vector3.new(self.HitboxSize, self.HitboxSize, self.HitboxSize)
                            hrp.Transparency = 0.5
                            hrp.Material = Enum.Material.ForceField
                            hrp.CanCollide = false -- Ensure damage registration
                        end
                    end

                    if player.Character then
                        applyHitbox(player.Character)
                    end

                    player.CharacterAdded:Connect(function(character)
                        applyHitbox(character)
                    end)
                end
            end
        else
            for _, player in pairs(Players:GetPlayers()) do
                if player ~= LocalPlayer and player.Character then
                    local hrp = player.Character:FindFirstChild("HumanoidRootPart")
                    if hrp then
                        hrp.Size = Vector3.new(2, 2, 1) -- Default size
                        hrp.Transparency = 0
                        hrp.Material = Enum.Material.Plastic
                        hrp.CanCollide = true
                    end
                end
            end
        end
    end)
end

-- Update hitbox settings
function RubyHub:UpdateHitboxSettings()
    if self.HitboxExpander then
        self:ImplementHitboxExpander()
    end
end

-- Implementation of Jail Bypass feature
function RubyHub:ImplementJailBypass()
    pcall(function()
        if self.JailBypass then
            if not self.JailBypassConnection then
                self.JailBypassConnection = RunService.Stepped:Connect(function()
                    local character = LocalPlayer.Character
                    if character and character:FindFirstChild("HumanoidRootPart") then
                        local hrp = character.HumanoidRootPart

                        -- Check if the player is jailed (e.g., anchored or moved to a specific position)
                        if hrp.Anchored or (hrp.Position.Y < -100 or hrp.Position.Y > 1000) then
                            -- Unjail the player by resetting their position and unanchoring
                            hrp.Anchored = false
                            hrp.CFrame = CFrame.new(Vector3.new(0, 10, 0)) -- Reset to a safe position
                        end
                    end
                end)
            end
        else
            if self.JailBypassConnection then
                self.JailBypassConnection:Disconnect()
                self.JailBypassConnection = nil
            end
        end
    end)
end

-- Implementation of No Recoil feature
function RubyHub:ImplementNoRecoil()
    pcall(function()
        if self.NoRecoil then
            -- Disable camera recoil
            if not self.NoRecoilCameraConnection then
                self.NoRecoilCameraConnection = RunService.RenderStepped:Connect(function()
                    if workspace.CurrentCamera then
                        workspace.CurrentCamera.CFrame = workspace.CurrentCamera.CFrame
                    end
                end)
            end

            -- Disable gun spread and animations
            for _, tool in pairs(LocalPlayer.Backpack:GetChildren()) do
                if tool:IsA("Tool") then
                    local function disableRecoil(tool)
                        for _, obj in pairs(tool:GetDescendants()) do
                            if obj:IsA("Animation") or obj:IsA("ParticleEmitter") or obj:IsA("Sound") then
                                obj:Destroy()
                            elseif obj:IsA("Script") or obj:IsA("LocalScript") then
                                obj.Disabled = true
                            end
                        end
                    end

                    disableRecoil(tool)
                    tool.DescendantAdded:Connect(function(descendant)
                        disableRecoil(tool)
                    end)
                end
            end

            LocalPlayer.Backpack.ChildAdded:Connect(function(tool)
                if tool:IsA("Tool") then
                    disableRecoil(tool)
                end
            end)
        else
            -- Re-enable default behavior
            if self.NoRecoilCameraConnection then
                self.NoRecoilCameraConnection:Disconnect()
                self.NoRecoilCameraConnection = nil
            end
        end
    end)
end

-- Implementation of Arsenal Anti-Cheat Bypass feature (Minimal Version)
function RubyHub:ImplementArsenalAntiCheatBypass()
    pcall(function()
        if self.ArsenalAntiCheatBypass then
            if not self.ArsenalAntiCheatBypassConnection then
                -- Display notification that bypass is active
                self:DisplayNotification("Arsenal Anti-Cheat Bypass activated - Minimal Mode", 3)

                -- Initialize storage for original values
                if not self.ArsenalBypassData then
                    self.ArsenalBypassData = {
                        OriginalValues = {},
                        ProcessedInstances = {}
                    }
                end

                -- Create a minimal connection that only blocks ban/kick events
                self.ArsenalAntiCheatBypassConnection = RunService.Heartbeat:Connect(function()
                    -- Only run every 2 seconds to minimize performance impact
                    if tick() % 2 > 0.1 then return end

                    -- Check if we're in Arsenal game
                    local isArsenal = false
                    pcall(function()
                        if game.PlaceId == 286090429 or -- Arsenal's place ID
                           game:GetService("MarketplaceService"):GetProductInfo(game.PlaceId).Name:lower():find("arsenal") then
                            isArsenal = true
                        end
                    end)

                    if not isArsenal then return end

                    -- MINIMAL PROTECTION - ONLY BLOCK BAN/KICK EVENTS
                    pcall(function()
                        -- Only process new remotes that we haven't seen before
                        for _, instance in pairs(game:GetService("ReplicatedStorage"):GetDescendants()) do
                            if instance:IsA("RemoteEvent") and not self.ArsenalBypassData.ProcessedInstances[instance] then
                                local name = instance.Name:lower()
                                self.ArsenalBypassData.ProcessedInstances[instance] = true

                                -- Only block remotes that are explicitly for banning/kicking
                                if name:find("ban") or name:find("kick") then
                                    -- Create a safe wrapper that prevents the remote from firing
                                    local oldFireServer = instance.FireServer
                                    instance.FireServer = function(...) return nil end
                                end
                            end
                        end
                    end)
                end)
            end
        else
            -- Disable the bypass
            if self.ArsenalAntiCheatBypassConnection then
                self.ArsenalAntiCheatBypassConnection:Disconnect()
                self.ArsenalAntiCheatBypassConnection = nil
            end

            -- Clear the bypass data
            self.ArsenalBypassData = nil

            -- Notify user that bypass has been disabled
            self:DisplayNotification("Arsenal Anti-Cheat Bypass deactivated", 3)
        end
    end)
end

-- Implementation of the Teleport Aimbot feature
function RubyHub:ImplementTeleportAimbot()
    pcall(function()
        if self.TeleportAimbot then
            if not self.TeleportAimbotConnection then
                self.TeleportAimbotConnection = RunService.Heartbeat:Connect(function()
                    -- Ensure this logic only affects Teleport Aimbot
                    if not self.TeleportAimbot then
                        if self.TeleportAimbotConnection then
                            self.TeleportAimbotConnection:Disconnect()
                            self.TeleportAimbotConnection = nil
                        end
                        return
                    end

                    local character = LocalPlayer.Character
                    if not character or not character:FindFirstChild("HumanoidRootPart") then return end

                    local hrp = character.HumanoidRootPart
                    local targetPlayer = nil

                    -- Find the next valid target
                    for _, player in pairs(Players:GetPlayers()) do
                        if player ~= LocalPlayer and player.Character and player.Character:FindFirstChild("HumanoidRootPart") and
                           player.Character:FindFirstChildOfClass("Humanoid") and
                           player.Character:FindFirstChildOfClass("Humanoid").Health > 0 then

                            -- Skip teammates if Team Check is enabled
                            if self.TeamCheck and self:IsTeamMember(player) then
                                continue
                            end

                            targetPlayer = player
                            break
                        end
                    end

                    -- Teleport to the target and lock onto their head
                    if targetPlayer and targetPlayer.Character and targetPlayer.Character:FindFirstChild("HumanoidRootPart") then
                        local targetHRP = targetPlayer.Character.HumanoidRootPart
                        local targetHead = targetPlayer.Character:FindFirstChild("Head")

                        -- Teleport to the target
                        hrp.CFrame = targetHRP.CFrame * CFrame.new(0, 0, -3) -- Position slightly behind the target

                        -- Lock onto the target's head
                        if targetHead then
                            Camera.CFrame = CFrame.new(Camera.CFrame.Position, targetHead.Position)
                        end
                    else
                        -- Reset target if no valid player is found
                        targetPlayer = nil
                    end
                end)
            end
        else
            if self.TeleportAimbotConnection then
                self.TeleportAimbotConnection:Disconnect()
                self.TeleportAimbotConnection = nil
            end
        end
    end)
end

-- Cleanup function
function RubyHub:Cleanup()
    -- Ensure all features are disabled independently
    self.Flying = false
    self.NoClip = false
    self.AimBot = false
    self.SilentAim = false
    self.ESP = false
    self.InfiniteJump = false
    self.AntiAFK = false
    self.TeamCheck = false
    self.SpazzAimbot = false
    self.HitboxExpander = false
    self.JailBypass = false
    self.NoRecoil = false
    self.TeleportAimbot = false
    self.ArsenalAntiCheatBypass = false

    -- Implement changes to disable features
    pcall(function() self:ImplementFly() end)
    pcall(function() self:ImplementNoClip() end)
    pcall(function() self:ImplementAimbot() end)
    pcall(function() self:ImplementSilentAim() end)
    pcall(function() self:ImplementESP() end)
    pcall(function() self:ImplementInfiniteJump() end)
    pcall(function() self:ImplementAntiAFK() end)
    pcall(function() self:ImplementSpazzAimbot() end)
    pcall(function() self:ImplementHitboxExpander() end)
    pcall(function() self:ImplementJailBypass() end)
    pcall(function() self:ImplementNoRecoil() end)
    pcall(function() self:ImplementTeleportAimbot() end)
    pcall(function() self:ImplementArsenalAntiCheatBypass() end)

    -- Disconnect all connections
    local connections = {
        "FlyConnection", "NoClipConnection", "AimbotConnection",
        "SilentAimConnection", "ESPConnection", "InfiniteJumpConnection",
        "SpazzAimbotConnection", "JailBypassConnection", "NoRecoilCameraConnection",
        "TeleportAimbotConnection", "ArsenalAntiCheatBypassConnection",
        "ArsenalAntiCheatBypassSecondaryConnection"
    }

    for _, conn in ipairs(connections) do
        if self[conn] then
            pcall(function() self[conn]:Disconnect() end)
            self[conn] = nil
        end
    end

    -- Clean up all drawing objects
    for player, obj in pairs(self.ESPObjects) do
        for key, value in pairs(obj) do
            if key == "Highlight" then
                pcall(function() value:Destroy() end)
            else
                pcall(function() value:Remove() end)
            end
        end
    end

    if self.AimCircle then
        pcall(function() self.AimCircle:Remove() end)
    end

    -- Clear tables
    self.ESPObjects = {}
    self.AimbotTarget = nil

    -- Clean up GUI
    if self.GUI then
        pcall(function() self.GUI:Destroy() end)
    end
end

-- Initialize the hub with input protection
pcall(function()
    -- Set up global input protection
    local inputDebounce = {}
    local lastInputTime = {}
    local inputCooldown = 0.2 -- 200ms cooldown between inputs

    -- Create a connection to protect against input spam
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        local inputKey = tostring(input.KeyCode.Value)
        local currentTime = tick()

        -- Skip if already pressed or too soon after last press
        if inputDebounce[inputKey] or (lastInputTime[inputKey] and currentTime - lastInputTime[inputKey] < inputCooldown) then
            return
        end

        -- Mark as pressed and record time
        inputDebounce[inputKey] = true
        lastInputTime[inputKey] = currentTime
    end)

    -- Clear pressed keys on InputEnded
    UserInputService.InputEnded:Connect(function(input, gameProcessed)
        local inputKey = tostring(input.KeyCode.Value)
        inputDebounce[inputKey] = nil
    end)

    RubyHub:InitializeUI()
    RubyHub:DisplayNotification("Ruby Hub loaded successfully!")
end)

return RubyHub